name: "Pipeline"
on:
  push:
  pull_request:
  workflow_dispatch:
jobs:
  ci:
    name: "CI"
    environment: CI
    runs-on: "ubuntu-latest"
    steps:
      - uses: "actions/checkout@v4"
        with:
          fetch-depth: 0
      - uses: "moonrepo/setup-toolchain@v0"
      - run: "moon ci --color"
        env:
          MOONBASE_SECRET_KEY: ${{ secrets.MOONBASE_SECRET_KEY }}
      - uses: moonrepo/run-report-action@v1
        if: success() || failure()
        with:
          access-token: ${{ secrets.GITHUB_TOKEN }}
          matrix: ${{ toJSON(matrix) }}