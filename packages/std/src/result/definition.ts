import type { BlobType, UnionToTuple } from '../shared'
import type { errSym, okSym } from './utils/result'

declare global {
  export namespace std {
    export type Ok<Type> = {
      [okSym]: Type
      [Symbol.iterator](): Generator<Type, Type, unknown>
    }

    export type Err<Name extends string, Causes extends string[]> = {
      [errSym]: [Name, string, Causes]
      [Symbol.iterator](): Generator<std.Err<Name, Causes>, std.Err<Name, Causes>, unknown>
    }

    export type Result<Type, Name extends string, Causes extends string[]> = Ok<Type> | Err<Name, Causes>

    export type ExtractOk<Type> = Type extends Ok<infer Type2>
      ? Type2
      : Type extends Err<BlobType, BlobType>
        ? never
        : Type
    export type ExtractName<Type> = Type extends Err<infer Name, BlobType> ? Name : never
    export type ExtractCauses<Type> = Type extends Err<BlobType, infer Causes> ? UnionToTuple<Causes[number]> : never
  }
}
