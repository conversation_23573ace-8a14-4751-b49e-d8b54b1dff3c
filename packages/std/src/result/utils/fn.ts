import type { BlobType } from '../../shared'

import { RESULT_EDGE_CASES } from '../const'

import { err } from './result'

export const fn = <Args extends BlobType[], Return extends BlobType, const Causes extends string[]>(
  cb: (...args: Args) => Return,
  ...causes: Causes
) => {
  return ((...args: Args) => {
    try {
      return cb(...args) as BlobType
    } catch (rawError) {
      return err(RESULT_EDGE_CASES.unexpected, 'An internal error occurred', ...causes) as BlobType
    }
  }) as (
    ...args: Args
  ) => std.Result<
    std.ExtractOk<Return>,
    std.ExtractName<Return>,
    std.ExtractCauses<Return>[number] extends never ? [] : std.ExtractCauses<Return>[number][]
  >
}
