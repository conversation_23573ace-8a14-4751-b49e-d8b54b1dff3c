import type { PackageJson } from 'type-fest'

import { type BuildEntry, build } from './utils/build'
import { buildTypes } from './utils/build-types'
import { splitBuild } from './utils/split-build'

export interface ActionOptions {
  name: string

  cwd: string
  watch: boolean
  json: boolean
  references: boolean
  format: 'cjs' | 'esm'
  silent: boolean

  target: 'bun' | 'browser' | 'node'
  env: 'development' | 'production' | 'test'
  packages: string[]

  exports: PackageJson['exports']
  splitBuilds: string[]
  external: string[]
}

export const action = async (options: ActionOptions) => {
  const allTargetPaths: string[] = []
  const buildEntries: BuildEntry[] = []
  const tsxBuildEntries: BuildEntry[] = []

  for (const [rawName, definition] of Object.entries(options.exports ?? {})) {
    const name = rawName === '.' ? 'default' : rawName.replace('./', '')

    // if user specified packages, skip all other exports
    if (options.packages.length > 0 && !options.packages.includes(name)) {
      continue
    }

    if (
      !definition ||
      typeof definition !== 'object' ||
      Array.isArray(definition) ||
      typeof definition.source !== 'string'
    ) {
      throw new Error(`Invalid exports definition ${name}`)
    }

    if (allTargetPaths.includes(definition.source)) {
      throw new Error(`Duplicate export path in definition ${name}`)
    }

    if (options.splitBuilds.includes(name)) {
      tsxBuildEntries.push({
        default: definition.default as string,
        name,
        source: definition.source,
        types: definition.types as string,
      })

      continue
    }

    buildEntries.push({
      default: definition.default as string,
      name,
      source: definition.source,
      types: definition.types as string,
    })
  }

  if (buildEntries.length > 0) {
    await build({
      cwd: options.cwd,

      entries: buildEntries,
      env: options.env,
      external: options.external,
      format: options.format,
      silent: options.silent,
      target: options.target,
    })
  }

  if (tsxBuildEntries.length > 0) {
    await splitBuild({
      cwd: options.cwd,

      entries: tsxBuildEntries,
      env: options.env,
      external: options.external,
      format: options.format,
      silent: options.silent,
      target: options.target,
    })
  }

  await buildTypes({
    cwd: options.cwd,

    entries: [...buildEntries, ...tsxBuildEntries],
    json: options.json,
    name: options.name,
    references: options.references,
    watch: options.watch,
  })
}
